{"$schema": "../../.rnv/schema/rnv.app.json", "extendsTemplate": "@rnv/template-starter/appConfigs/base/renative.json", "id": "app", "common": {"runtime": {"welcomeMessage": "HandballTV"}, "title": "HandballTV", "id": "com.odott.handballtv.devtv", "description": "HandballTV"}, "platforms": {"tvos": {"buildSchemes": {"appstore": {"teamID": "AEN82QY5V4", "runScheme": "Release", "bundleAssets": true, "provisioningStyle": "Manual", "codeSignIdentity": "iPhone Distribution", "provisionProfileSpecifier": "Handball tvOS POC Provis Profile", "exportOptions": {"method": "app-store", "uploadBitcode": false, "compileBitcode": false, "uploadSymbols": true, "signingStyle": "manual", "signingCertificate": "iPhone Distribution", "provisioningProfiles": {"com.odott.handballtv.devtv": "Handball tvOS POC Provis Profile"}}}}}, "androidtv": {"id": "com.odott.handballtv.devtv", "versionCode": "211", "buildSchemes": {"release": {"signingConfig": "Release", "bundleAssets": true, "entryFile": "index.js", "bundleIsDev": false, "storeFile": "/Users/<USER>/OTT_TV_POC/appConfigs/app/ott_tv_demo.jks", "keyAlias": "ott_tv_demo", "storePassword": "nEiJRx5g0Tkfx8kmfU4d", "keyPassword": "nEiJRx5g0Tkfx8kmfU4d", "aab": true}}, "templateAndroid": {"AndroidManifest_xml": {"tag": "manifest", "children": [{"tag": "application", "android:name": ".MainApplication", "android:icon": "@mipmap/ic_launcher", "android:roundIcon": "@mipmap/ic_launcher", "android:banner": "@mipmap/ic_banner", "children": [{"tag": "activity", "android:name": ".MainActivity", "android:windowSoftInputMode": "adjustPan", "android:exported": "true", "android:launchMode": "singleTop", "children": [{"tag": "intent-filter", "children": [{"tag": "action", "android:name": "android.intent.action.MAIN"}, {"tag": "category", "android:name": "android.intent.category.LAUNCHER"}, {"tag": "category", "android:name": "android.intent.category.LEANBACK_LAUNCHER"}]}]}]}]}}, "assetFolderPlatform": "androidtv", "assetSources": []}, "tizen": {"id": "com.odott.handballtv.devtv", "package": "wgt", "version": "1.0.2", "certificateProfile": "RNVanillaCert", "buildSchemes": {"debug": {"bundleAssets": false, "bundleIsDev": true}, "release": {"bundleAssets": true, "bundleIsDev": false}}, "assetSources": ["appConfigs/app/assets"]}, "webos": {"id": "com.odott.handballtv.devtv", "version": "1.0.2", "buildSchemes": {"debug": {"bundleAssets": false, "bundleIsDev": true}, "release": {"bundleAssets": true, "bundleIsDev": false}}, "assetSources": ["appConfigs/app/assets"], "assetFolderPlatform": "webos"}}}