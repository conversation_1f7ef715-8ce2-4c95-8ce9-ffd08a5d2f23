import React, { useState, memo, useCallback } from "react";
import {
	View,
	Text,
	TextInput,
	TouchableOpacity,
	StyleSheet,
	ActivityIndicator,
	KeyboardAvoidingView,
	Platform,
} from "react-native";
import { GLOBAL_STYLES } from "../styles/globalStyles";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../app/index";
import { scale } from "../utils/helpers/dimensionScale.helper";
import {
	AuthService,
	LoginCredentials,
} from "../services/authService";
import { QRCodeSection } from "../components/common";

/**
 * LoginPage Component
 * Displays login form alongside QR code for account management
 * Optimized with React.memo for performance
 */
const LoginPage = memo(() => {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	const navigation =
		useNavigation<NativeStackNavigationProp<RootStackParamList>>();

	// Memoized handlers for performance
	const handleEmailChange = useCallback(
		(text: string) => {
			setEmail(text);
			// Clear error when user starts typing
			if (error) setError("");
		},
		[error]
	);

	const handlePasswordChange = useCallback(
		(text: string) => {
			setPassword(text);
			// Clear error when user starts typing
			if (error) setError("");
		},
		[error]
	);

	/**
	 * Handle login form submission
	 * Validates input and calls authentication service
	 */
	const handleLogin = async () => {
		// Clear previous errors
		setError("");

		// Validate input
		if (!email.trim()) {
			setError("Email is required");
			return;
		}

		if (!AuthService.validateEmail(email)) {
			setError("Please enter a valid email address");
			return;
		}

		if (!password.trim()) {
			setError("Password is required");
			return;
		}

		if (!AuthService.validatePassword(password)) {
			setError("Password must be at least 1 characters long");
			return;
		}

		// Start loading
		setIsLoading(true);

		try {
			console.log("🔐 Starting login attempt for:", email);
			console.log("Password details:", {
				length: password.length,
				chars: password.split("").map((c) => c.charCodeAt(0)),
				raw: password,
			});
			const credentials: LoginCredentials = {
				email: email.trim(), // Only trim email
				password: password, // Use password as is
			};
			const result = await AuthService.login(credentials); // Use the main login method

			if (result.success) {
				// Login successful, navigate back to home page
				console.log(
					"✅ Login successful! Navigating back to HomePage"
				);
				console.log(
					"🏠 HomePage will re-check authentication status on focus"
				);
				navigation.goBack(); // Go back to previous screen (HomePage)
			} else {
				// Login failed, show error
				console.log("❌ Login failed:", result.error);
				setError(result.error || "Login failed. Please try again.");
			}
		} catch (error) {
			console.log("💥 Unexpected login error:", error);
			setError("An unexpected error occurred. Please try again.");
		} finally {
			// Stop loading
			setIsLoading(false);
		}
	};

	return (
		<KeyboardAvoidingView
			style={styles.pageBackground}
			behavior={Platform.OS === "android" ? "height" : "padding"}
			keyboardVerticalOffset={0}
		>
			<View style={styles.mainContainer}>
				{/* Left side - Login Form */}
				<View style={styles.loginSection}>
					<View style={styles.card}>
						<Text style={styles.title}>LOG IN</Text>

						{/* Error message display */}
						{error ? (
							<View style={styles.errorContainer}>
								<Text style={styles.errorText}>{error}</Text>
							</View>
						) : null}

						<View style={styles.formGroup}>
							<Text style={styles.label}>Email</Text>
							<TextInput
								style={[
									styles.input,
									error ? styles.inputError : null,
								]}
								value={email}
								onChangeText={handleEmailChange}
								placeholder="Enter your email"
								placeholderTextColor={
									GLOBAL_STYLES.COLORS.TEXT_TERTIARY
								}
								autoCapitalize="none"
								keyboardType="email-address"
								editable={!isLoading}
							/>
						</View>
						<View style={styles.formGroup}>
							<Text style={styles.label}>Password</Text>
							<TextInput
								style={[
									styles.input,
									error ? styles.inputError : null,
								]}
								value={password}
								onChangeText={handlePasswordChange}
								placeholder="Enter your password"
								placeholderTextColor={
									GLOBAL_STYLES.COLORS.TEXT_TERTIARY
								}
								secureTextEntry
								editable={!isLoading}
							/>
						</View>
						<TouchableOpacity
							style={[
								styles.button,
								{ opacity: isLoading ? 0.5 : 0.7 },
								isLoading && styles.buttonDisabled,
							]}
							onPress={handleLogin}
							activeOpacity={1}
							disabled={isLoading}
						>
							{isLoading ? (
								<View style={styles.buttonContent}>
									<ActivityIndicator
										size="small"
										color={GLOBAL_STYLES.COLORS.TEXT_PRIMARY}
										style={styles.loadingIndicator}
									/>
									<Text style={styles.buttonText}>Logging in...</Text>
								</View>
							) : (
								<Text style={styles.buttonText}>Log in</Text>
							)}
						</TouchableOpacity>
					</View>
				</View>

				{/* Right side - QR Code Section */}
				<View style={styles.qrSection}>
					<QRCodeSection
						title="TO MANAGE ACCOUNT AND SUBSCRIPTION PLEASE FOLLOW THE QR CODE"
						showTitle={true}
					/>
				</View>
			</View>
		</KeyboardAvoidingView>
	);
});

const styles = StyleSheet.create({
	pageBackground: {
		flex: 1,
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		justifyContent: "center",
		alignItems: "center",
		// Ensure the layout doesn't shift when keyboard appears on Android TV
		position: "relative",
	},
	mainContainer: {
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "center",
		width: "100%",
		maxWidth: scale(1600),
		paddingHorizontal: scale(64),
	},
	loginSection: {
		flex: 1,
		alignItems: "center",
		justifyContent: "center",
		paddingRight: scale(80),
	},
	qrSection: {
		flex: 1,
		alignItems: "flex-start",
		justifyContent: "center",
		paddingLeft: scale(80),
	},
	card: {
		backgroundColor: "#131c2b",
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		padding: scale(80),
		minWidth: scale(700),
		width: "100%",
		maxWidth: scale(800),
		alignItems: "center",
		shadowOpacity: 0.2,
		shadowRadius: scale(8),
		elevation: scale(4),
	},
	title: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(64),
		fontWeight: "bold",
		marginBottom: scale(64),
		letterSpacing: scale(2),
	},
	formGroup: {
		width: "100%",
		marginBottom: scale(48),
	},
	label: {
		color: GLOBAL_STYLES.COLORS.TEXT_SECONDARY,
		fontSize: scale(32),
		marginBottom: scale(16),
		marginLeft: scale(2),
	},
	input: {
		backgroundColor: "#18243a",
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		paddingVertical: scale(28),
		paddingHorizontal: scale(32),
		fontSize: scale(32),
		borderWidth: scale(2),
		borderColor: "#22304a",
	},
	button: {
		backgroundColor: GLOBAL_STYLES.COLORS.ACCENT,
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		paddingVertical: scale(28),
		paddingHorizontal: 0,
		marginTop: scale(32),
		width: "100%",
		alignItems: "center",
	},
	buttonText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(36),
		fontWeight: "bold",
		letterSpacing: scale(1),
	},
	buttonDisabled: {
		opacity: 0.5,
	},
	buttonContent: {
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "center",
	},
	loadingIndicator: {
		marginRight: scale(12),
	},
	errorContainer: {
		backgroundColor: "#ff4444",
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		padding: scale(16),
		marginBottom: scale(24),
		width: "100%",
	},
	errorText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(24),
		textAlign: "center",
	},
	inputError: {
		borderColor: "#ff4444",
		borderWidth: scale(2),
	},
});

// Set display name for debugging
LoginPage.displayName = "LoginPage";

export default LoginPage;
