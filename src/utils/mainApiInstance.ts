import { MainAPI } from "./apis/generated/main_api";

export const mainAPIClient = new MainAPI({
	baseURL: "https://api-gateway.onrewind.tv/main-api",
	headers: {
		Accept: "application/json",
		"Accept-Charset": "UTF-8",
		"x-account-key": "ByAWCu-i5",
	},
});

export const setMainApiAuthToken = (token: string | null) => {
	mainAPIClient.instance.defaults.headers.common["Authorization"] =
		token ? `Bearer ${token}` : "";
};

// Set language parameter for main API requests as query parameter
export const setMainApiLanguage = (language: string) => {
	// Set default params for all requests to include language
	mainAPIClient.instance.defaults.params = {
		...mainAPIClient.instance.defaults.params,
		language: language,
	};
};
