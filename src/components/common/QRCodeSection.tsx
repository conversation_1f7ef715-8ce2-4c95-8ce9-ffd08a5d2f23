import React, { memo } from "react";
import { View, Text, Image, StyleSheet } from "react-native";
import { GLOBAL_STYLES } from "../../styles/globalStyles";
import { scale } from "../../utils/helpers/dimensionScale.helper";

// Import the QR code image from assets
const QR_CODE_IMAGE = require("../../assets/images/qr-image.png");

interface QRCodeSectionProps {
	title?: string;
	subtitle?: string;
	showTitle?: boolean;
	style?: any;
}

/**
 * QRCodeSection Component
 * Displays a QR code with customizable title and subtitle text
 * Used for subscription management and account access flows
 * Optimized with React.memo for performance
 */
const QRCodeSection: React.FC<QRCodeSectionProps> = memo(
	({
		title = "TO MANAGE ACCOUNT AND SUBSCRIPTION PLEASE FOLLOW THE QR CODE",
		subtitle,
		showTitle = true,
		style,
	}) => {
		return (
			<View style={[styles.container, style]}>
				{showTitle && (
					<View style={styles.textContainer}>
						<Text style={styles.title}>{title}</Text>
						{subtitle && (
							<Text style={styles.subtitle}>{subtitle}</Text>
						)}
					</View>
				)}
				<View style={styles.qrContainer}>
					<Image
						source={QR_CODE_IMAGE}
						style={styles.qrImage}
						resizeMode="contain"
						testID="qr-code-image"
					/>
				</View>
			</View>
		);
	}
);

const styles = StyleSheet.create({
	container: {
		alignItems: "center", // Center everything horizontally
		justifyContent: "center",
		paddingVertical: scale(0),
		width: "100%", // Take full width to allow centering
	},
	textContainer: {
		marginBottom: scale(64),
		alignItems: "center", // Center text
		maxWidth: scale(700),
		width: "100%",
	},
	title: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(36),
		fontWeight: "bold",
		textAlign: "center", // Center text
		lineHeight: scale(48),
		letterSpacing: scale(1.5),
	},
	subtitle: {
		color: GLOBAL_STYLES.COLORS.TEXT_SECONDARY,
		fontSize: scale(24),
		textAlign: "center", // Center text
		marginTop: scale(16),
		lineHeight: scale(32),
	},
	qrContainer: {
		backgroundColor: "transparent",
		borderRadius: scale(20),
		padding: scale(0),
		alignSelf: "center",
	},
	qrImage: {
		width: scale(500), // Increased size
		height: scale(500), // Increased size
	},
});

// Set display name for debugging
QRCodeSection.displayName = "QRCodeSection";

export default QRCodeSection;
