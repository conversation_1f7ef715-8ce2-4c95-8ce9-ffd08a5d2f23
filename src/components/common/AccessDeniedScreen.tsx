import React, { useState, memo, useCallback } from "react";
import { View, Text, Pressable, StyleSheet } from "react-native";
import { GLOBAL_STYLES } from "../../styles/globalStyles";
import { scale } from "../../utils/helpers/dimensionScale.helper";
import QRCodeSection from "./QRCodeSection";

interface AccessDeniedScreenProps {
	onCancel: () => void;
	title?: string;
	subtitle?: string;
}

/**
 * AccessDeniedScreen Component
 * Full-screen component shown when user doesn't have access to paid content
 * Displays QR code for subscription management with a cancel button
 * Designed for TV navigation with auto-focus support
 * Optimized with React.memo for performance
 */
const AccessDeniedScreen: React.FC<AccessDeniedScreenProps> = memo(
	({
		onCancel,
		title = "YOU DON'T HAVE ACCESS TO THIS VIDEO.",
		subtitle = "TO MANAGE THE SUBSCRIPTION PLEASE FOLLOW THE QR CODE",
	}) => {
		const [isFocused, setIsFocused] = useState(false);

		// Memoized focus handlers for performance
		const handleFocus = useCallback(() => setIsFocused(true), []);
		const handleBlur = useCallback(() => setIsFocused(false), []);

		return (
			<View style={styles.container}>
				<View style={styles.content}>
					{/* Main title */}
					<Text style={styles.mainTitle}>{title}</Text>

					{/* QR Code Section */}
					<QRCodeSection
						title={subtitle}
						showTitle={true}
						style={styles.qrSection}
					/>

					{/* Cancel Button */}
					<Pressable
						style={[
							styles.cancelButton,
							isFocused && styles.cancelButtonFocused,
						]}
						onPress={onCancel}
						onFocus={handleFocus}
						onBlur={handleBlur}
						hasTVPreferredFocus={true} // Auto-focus on TV platforms
						focusable={true}
						accessible={true}
						accessibilityRole="button"
						accessibilityLabel="Cancel and go back"
					>
						<Text
							style={[
								styles.cancelButtonText,
								isFocused && styles.cancelButtonTextFocused,
							]}
						>
							CANCEL
						</Text>
					</Pressable>
				</View>
			</View>
		);
	}
);

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		justifyContent: "center",
		alignItems: "center",
	},
	content: {
		alignItems: "center",
		justifyContent: "center",
		paddingHorizontal: scale(64),
		maxWidth: scale(1200),
		width: "100%",
	},
	mainTitle: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(48),
		fontWeight: "bold",
		textAlign: "center",
		marginBottom: scale(64),
		letterSpacing: scale(2),
		lineHeight: scale(60),
	},
	qrSection: {
		marginBottom: scale(80),
	},
	cancelButton: {
		backgroundColor: GLOBAL_STYLES.COLORS.ACCENT,
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		paddingVertical: scale(24),
		paddingHorizontal: scale(80),
		minWidth: scale(300),
		alignItems: "center",
		shadowColor: "#000",
		shadowOffset: { width: 0, height: scale(4) },
		shadowOpacity: 0.3,
		shadowRadius: scale(8),
		elevation: 8,
	},
	cancelButtonFocused: {
		backgroundColor: "#ff6666", // Lighter red when focused
		transform: [{ scale: 1.05 }], // Slightly larger when focused
	},
	cancelButtonText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(36),
		fontWeight: "bold",
		letterSpacing: scale(2),
	},
	cancelButtonTextFocused: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
	},
});

// Set display name for debugging
AccessDeniedScreen.displayName = "AccessDeniedScreen";

export default AccessDeniedScreen;
